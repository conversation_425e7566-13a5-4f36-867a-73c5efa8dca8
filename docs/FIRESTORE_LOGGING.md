# Firestore Verbose Logging Guide

This document explains how to enable and use verbose logging for Firestore operations in your Flash application.

## Overview

The application includes comprehensive Firestore logging capabilities that help you:
- Monitor all Firestore operations (reads, writes, queries, deletes)
- Track operation performance and timing
- Debug connection issues
- Analyze usage patterns
- Identify bottlenecks and errors

## Quick Start

### 1. Enable Verbose Logging

Add these environment variables to your `.env` file:

```bash
# Enable verbose Firestore logging
FIRESTORE_VERBOSE_LOGGING=true

# Enable detailed operation logging
FIRESTORE_LOG_OPERATIONS=true
```

### 2. Restart Your Development Server

```bash
npm run dev
```

### 3. Monitor Logs

Open your browser's developer console to see detailed Firestore operation logs.

## Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `FIRESTORE_VERBOSE_LOGGING` | Enable/disable verbose logging | `false` |
| `FIRESTORE_LOG_OPERATIONS` | Log individual operations | `false` |
| `NODE_ENV` | When set to `development`, enables logging automatically | - |

### Client-Side Configuration

The client-side Firebase configuration automatically enables verbose logging when:
- `NODE_ENV=development` OR
- `NEXT_PUBLIC_FIRESTORE_VERBOSE_LOGGING=true`

### Server-Side Configuration

The Firebase Admin SDK enables verbose logging when:
- `NODE_ENV=development` OR
- `FIRESTORE_VERBOSE_LOGGING=true`

## Using the Firestore Logger

### Basic Usage

```typescript
import { logFirestoreRead, logFirestoreWrite } from '@/lib/firestore-logger';

// Log a read operation
logFirestoreRead('users', 'user123', { source: 'profile-page' });

// Log a write operation
logFirestoreWrite('flashcardSets', 'set456', { 
  operation: 'create',
  cardCount: 25 
});
```

### Advanced Usage

```typescript
import { firestoreLogger } from '@/lib/firestore-logger';

// Log with timing
const startTime = Date.now();
// ... perform Firestore operation
firestoreLogger.logTiming({
  type: 'query',
  collection: 'flashcardSets',
  metadata: { filters: ['userId', 'createdAt'] }
}, startTime);

// Get operation statistics
const stats = firestoreLogger.getStats();
console.log('Firestore Stats:', stats);
```

## Development Tools

### Auto-Monitoring

When `FIRESTORE_VERBOSE_LOGGING=true`, the application automatically:
- Starts monitoring Firestore operations
- Logs statistics every 30 seconds
- Enables Firebase SDK debug logging

### Manual Control

```typescript
import { 
  startFirestoreMonitoring,
  stopFirestoreMonitoring,
  logFirestoreStats,
  testFirestoreConnection 
} from '@/lib/firestore-dev-tools';

// Start monitoring
startFirestoreMonitoring(10000); // Log stats every 10 seconds

// Test connection
await testFirestoreConnection();

// Get current stats
logFirestoreStats();

// Stop monitoring
stopFirestoreMonitoring();
```

## Log Output Examples

### Operation Logs

```
[Firestore READ] exchangeRates/current (45ms)
[Firestore WRITE] userUsage/user123 (23ms)
[Firestore QUERY] flashcardSets (156ms)
[Firestore DELETE] flashcardSets/set456 (34ms)
```

### Statistics Logs

```
[Firestore Dev Tools] Operation Statistics: {
  totalOperations: 47,
  operationsByType: { read: 23, write: 15, query: 8, delete: 1 },
  averageDuration: "67.34ms",
  errors: 2,
  errorRate: "4.3%"
}
```

### Error Logs

```
[Firestore READ] exchangeRates/current - ERROR: Permission denied
```

## Firebase SDK Debug Logging

When verbose logging is enabled, the Firebase SDK will also output debug information:

```
Firebase: Firestore (9.x.x): Connection WebChannel transport started.
Firebase: Firestore (9.x.x): RestConnection sending request: ...
Firebase: Firestore (9.x.x): Received RPC response: ...
```

## Performance Considerations

### Development Only

All logging is automatically disabled in production (`NODE_ENV=production`) to:
- Prevent sensitive information leakage
- Maintain optimal performance
- Reduce bundle size

### Memory Usage

The logger maintains a history of operations in memory. In long-running development sessions:
- Use `clearFirestoreHistory()` to clear the history
- Monitor memory usage if needed

## Troubleshooting

### No Logs Appearing

1. Check environment variables are set correctly
2. Ensure you're in development mode
3. Verify browser console is open
4. Check that operations are actually being performed

### Too Many Logs

1. Set `FIRESTORE_LOG_OPERATIONS=false` to reduce verbosity
2. Use `stopFirestoreMonitoring()` to stop automatic stats logging
3. Adjust monitoring interval: `startFirestoreMonitoring(60000)` for 1-minute intervals

### Performance Issues

1. Disable logging: `FIRESTORE_VERBOSE_LOGGING=false`
2. Clear history regularly: `clearFirestoreHistory()`
3. Reduce monitoring frequency

## Integration with Existing Code

The logging system is already integrated with:
- `src/lib/currency-service.ts` - Exchange rate operations
- `src/lib/firebase-admin.ts` - Admin SDK operations
- `src/lib/firebase.ts` - Client SDK initialization

To add logging to new code:

```typescript
import { logFirestoreRead } from '@/lib/firestore-logger';

async function fetchUserData(userId: string) {
  try {
    const userDoc = await getDoc(doc(db, 'users', userId));
    
    // Log the operation
    logFirestoreRead('users', userId, {
      exists: userDoc.exists(),
      source: 'user-service'
    });
    
    return userDoc.data();
  } catch (error) {
    // Error logging is handled automatically
    throw error;
  }
}
```

## Security Notes

- All logging is disabled in production
- No sensitive data should be logged in metadata
- Environment variables are not exposed to the client unless prefixed with `NEXT_PUBLIC_`
- Firebase credentials are never logged
