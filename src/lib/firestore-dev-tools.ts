/**
 * Firestore Development Tools
 * 
 * Utilities for monitoring and debugging Firestore operations during development.
 * Only active in development mode.
 */

import { firestoreLogger } from './firestore-logger';
import { logger } from './logger';

class FirestoreDevTools {
  private isEnabled: boolean;
  private statsInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.isEnabled = process.env.NODE_ENV === 'development';
  }

  /**
   * Start monitoring Firestore operations
   */
  startMonitoring(intervalMs: number = 30000) {
    if (!this.isEnabled) return;

    logger.log('[Firestore Dev Tools] Starting operation monitoring');

    this.statsInterval = setInterval(() => {
      this.logStats();
    }, intervalMs);

    // Log stats immediately
    this.logStats();
  }

  /**
   * Stop monitoring
   */
  stopMonitoring() {
    if (this.statsInterval) {
      clearInterval(this.statsInterval);
      this.statsInterval = null;
      logger.log('[Firestore Dev Tools] Stopped operation monitoring');
    }
  }

  /**
   * Log current Firestore operation statistics
   */
  logStats() {
    if (!this.isEnabled) return;

    const stats = firestoreLogger.getStats();
    if (!stats) return;

    logger.log('[Firestore Dev Tools] Operation Statistics:', {
      totalOperations: stats.totalOperations,
      operationsByType: stats.operationsByType,
      averageDuration: `${stats.averageDuration.toFixed(2)}ms`,
      errors: stats.errors,
      errorRate: stats.totalOperations > 0 ? 
        `${((stats.errors / stats.totalOperations) * 100).toFixed(1)}%` : '0%'
    });
  }

  /**
   * Clear operation history
   */
  clearHistory() {
    if (!this.isEnabled) return;
    
    firestoreLogger.clearHistory();
    logger.log('[Firestore Dev Tools] Cleared operation history');
  }

  /**
   * Enable verbose console logging for all Firebase operations
   */
  enableVerboseLogging() {
    if (!this.isEnabled) return;

    if (typeof window !== 'undefined') {
      // Enable Firebase debug logging
      localStorage.setItem('firebase:logging', 'true');
      localStorage.setItem('firebase:debug', 'true');
      
      // Enable Firestore specific logging
      (window as any).FIREBASE_FIRESTORE_DEBUG = true;
      
      logger.log('[Firestore Dev Tools] Enabled verbose Firebase logging');
    }
  }

  /**
   * Disable verbose console logging
   */
  disableVerboseLogging() {
    if (!this.isEnabled) return;

    if (typeof window !== 'undefined') {
      localStorage.removeItem('firebase:logging');
      localStorage.removeItem('firebase:debug');
      delete (window as any).FIREBASE_FIRESTORE_DEBUG;
      
      logger.log('[Firestore Dev Tools] Disabled verbose Firebase logging');
    }
  }

  /**
   * Log current Firebase configuration
   */
  logFirebaseConfig() {
    if (!this.isEnabled) return;

    const config = {
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
      storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
      messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
      appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
      verboseLogging: process.env.FIRESTORE_VERBOSE_LOGGING,
      logOperations: process.env.FIRESTORE_LOG_OPERATIONS
    };

    logger.log('[Firestore Dev Tools] Firebase Configuration:', config);
  }

  /**
   * Test Firestore connection
   */
  async testConnection() {
    if (!this.isEnabled) return;

    try {
      logger.log('[Firestore Dev Tools] Testing Firestore connection...');
      
      const { db, doc, getDoc } = await import('./firebase');
      const testDoc = doc(db, 'test', 'connection');
      
      const startTime = Date.now();
      await getDoc(testDoc);
      const duration = Date.now() - startTime;
      
      logger.log('[Firestore Dev Tools] Connection test completed', {
        duration: `${duration}ms`,
        status: 'success'
      });
    } catch (error) {
      logger.error('[Firestore Dev Tools] Connection test failed:', error);
    }
  }

  /**
   * Monitor specific collection operations
   */
  monitorCollection(collectionName: string) {
    if (!this.isEnabled) return;

    logger.log(`[Firestore Dev Tools] Monitoring collection: ${collectionName}`);
    
    // This would require more advanced instrumentation
    // For now, just log that monitoring is requested
    logger.log(`[Firestore Dev Tools] Collection monitoring for '${collectionName}' is active`);
  }
}

// Export singleton instance
export const firestoreDevTools = new FirestoreDevTools();

// Convenience functions for easy use in development
export const startFirestoreMonitoring = (intervalMs?: number) => 
  firestoreDevTools.startMonitoring(intervalMs);

export const stopFirestoreMonitoring = () => 
  firestoreDevTools.stopMonitoring();

export const logFirestoreStats = () => 
  firestoreDevTools.logStats();

export const clearFirestoreHistory = () => 
  firestoreDevTools.clearHistory();

export const enableFirestoreVerboseLogging = () => 
  firestoreDevTools.enableVerboseLogging();

export const disableFirestoreVerboseLogging = () => 
  firestoreDevTools.disableVerboseLogging();

export const testFirestoreConnection = () => 
  firestoreDevTools.testConnection();

export const logFirebaseConfig = () => 
  firestoreDevTools.logFirebaseConfig();

// Auto-start monitoring in development if enabled
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Start monitoring after a short delay to allow Firebase to initialize
  setTimeout(() => {
    if (process.env.FIRESTORE_VERBOSE_LOGGING === 'true') {
      firestoreDevTools.enableVerboseLogging();
      firestoreDevTools.startMonitoring();
      firestoreDevTools.logFirebaseConfig();
    }
  }, 2000);
}
