/**
 * Firestore Logging Utility
 * 
 * Provides comprehensive logging for Firestore operations including
 * query performance, document access patterns, and error tracking.
 */

import { logger } from './logger';

interface FirestoreOperation {
  type: 'read' | 'write' | 'delete' | 'query';
  collection: string;
  document?: string;
  timestamp: number;
  duration?: number;
  error?: string;
  metadata?: Record<string, any>;
}

class FirestoreLogger {
  private operations: FirestoreOperation[] = [];
  private isEnabled: boolean;
  private logOperations: boolean;

  constructor() {
    this.isEnabled = process.env.FIRESTORE_VERBOSE_LOGGING === 'true' || 
                     process.env.NODE_ENV === 'development';
    this.logOperations = process.env.FIRESTORE_LOG_OPERATIONS === 'true';
  }

  /**
   * Log a Firestore operation
   */
  logOperation(operation: Omit<FirestoreOperation, 'timestamp'>) {
    if (!this.isEnabled) return;

    const fullOperation: FirestoreOperation = {
      ...operation,
      timestamp: Date.now()
    };

    this.operations.push(fullOperation);

    if (this.logOperations) {
      const logMessage = this.formatOperationLog(fullOperation);
      
      if (operation.error) {
        logger.error(logMessage, { error: operation.error, metadata: operation.metadata });
      } else {
        logger.log(logMessage, operation.metadata);
      }
    }
  }

  /**
   * Log a read operation
   */
  logRead(collection: string, document?: string, metadata?: Record<string, any>) {
    this.logOperation({
      type: 'read',
      collection,
      document,
      metadata
    });
  }

  /**
   * Log a write operation
   */
  logWrite(collection: string, document?: string, metadata?: Record<string, any>) {
    this.logOperation({
      type: 'write',
      collection,
      document,
      metadata
    });
  }

  /**
   * Log a delete operation
   */
  logDelete(collection: string, document?: string, metadata?: Record<string, any>) {
    this.logOperation({
      type: 'delete',
      collection,
      document,
      metadata
    });
  }

  /**
   * Log a query operation
   */
  logQuery(collection: string, metadata?: Record<string, any>) {
    this.logOperation({
      type: 'query',
      collection,
      metadata
    });
  }

  /**
   * Log an error in Firestore operation
   */
  logError(operation: Omit<FirestoreOperation, 'timestamp'>, error: Error) {
    this.logOperation({
      ...operation,
      error: error.message,
      metadata: {
        ...operation.metadata,
        errorStack: error.stack
      }
    });
  }

  /**
   * Log operation timing
   */
  logTiming(operation: Omit<FirestoreOperation, 'timestamp'>, startTime: number) {
    const duration = Date.now() - startTime;
    this.logOperation({
      ...operation,
      duration,
      metadata: {
        ...operation.metadata,
        performanceMs: duration
      }
    });
  }

  /**
   * Get operation statistics
   */
  getStats() {
    if (!this.isEnabled) return null;

    const stats = {
      totalOperations: this.operations.length,
      operationsByType: {} as Record<string, number>,
      averageDuration: 0,
      errors: 0
    };

    let totalDuration = 0;
    let operationsWithDuration = 0;

    this.operations.forEach(op => {
      stats.operationsByType[op.type] = (stats.operationsByType[op.type] || 0) + 1;
      
      if (op.duration) {
        totalDuration += op.duration;
        operationsWithDuration++;
      }
      
      if (op.error) {
        stats.errors++;
      }
    });

    if (operationsWithDuration > 0) {
      stats.averageDuration = totalDuration / operationsWithDuration;
    }

    return stats;
  }

  /**
   * Clear operation history
   */
  clearHistory() {
    this.operations = [];
  }

  /**
   * Format operation log message
   */
  private formatOperationLog(operation: FirestoreOperation): string {
    const { type, collection, document, duration, error } = operation;
    
    let message = `[Firestore ${type.toUpperCase()}] ${collection}`;
    
    if (document) {
      message += `/${document}`;
    }
    
    if (duration) {
      message += ` (${duration}ms)`;
    }
    
    if (error) {
      message += ` - ERROR: ${error}`;
    }
    
    return message;
  }
}

// Export singleton instance
export const firestoreLogger = new FirestoreLogger();

// Export utility functions for easy use
export const logFirestoreRead = (collection: string, document?: string, metadata?: Record<string, any>) => 
  firestoreLogger.logRead(collection, document, metadata);

export const logFirestoreWrite = (collection: string, document?: string, metadata?: Record<string, any>) => 
  firestoreLogger.logWrite(collection, document, metadata);

export const logFirestoreDelete = (collection: string, document?: string, metadata?: Record<string, any>) => 
  firestoreLogger.logDelete(collection, document, metadata);

export const logFirestoreQuery = (collection: string, metadata?: Record<string, any>) => 
  firestoreLogger.logQuery(collection, metadata);

export const logFirestoreError = (operation: Omit<FirestoreOperation, 'timestamp'>, error: Error) => 
  firestoreLogger.logError(operation, error);

export const logFirestoreTiming = (operation: Omit<FirestoreOperation, 'timestamp'>, startTime: number) => 
  firestoreLogger.logTiming(operation, startTime);
