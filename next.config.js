/** @type {import('next').NextConfig} */
const nextConfig = {
  /* config options here */
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
    ],
  },
  experimental: {
    serverActions: {
      bodySizeLimit: '20mb', // 20MB limit to account for Base64 encoding overhead
    },
  },
  env: {
    // Make environment variables available to the client
    GEMINI_MODEL: process.env.GEMINI_MODEL || 'googleai/gemini-2.0-flash',
    // Firestore logging configuration
    NEXT_PUBLIC_FIRESTORE_VERBOSE_LOGGING: process.env.FIRESTORE_VERBOSE_LOGGING,
    NEXT_PUBLIC_FIRESTORE_LOG_OPERATIONS: process.env.FIRESTORE_LOG_OPERATIONS,
  },
};

module.exports = nextConfig; 